# 视频连接优化和自动清理功能说明

## 功能概述

本次修改主要实现了两个重要功能：

1. **视频连接失败处理优化** - 当视频源连接失败时，系统会跳过该通道并显示"视频未连接"状态
2. **自动清理功能** - 每15天自动清空detection_results文件夹中的检测结果

## 详细功能说明

### 1. 视频连接失败处理优化

#### 主要改进：
- **智能重连机制**：每个通道最多尝试连接5次，每次间隔2秒
- **失败通道跳过**：达到最大重连次数后，系统会跳过该通道，继续处理其他通道
- **状态显示优化**：连接失败的通道会显示"视频未连接"状态和详细信息
- **资源管理**：连接失败的通道不会占用系统资源

#### 实现细节：
```python
# 连接参数
self.max_connection_attempts = 5  # 最大连接尝试次数
self.reconnect_delay = 2.0        # 重连间隔（秒）
self.connection_failed = False    # 连接失败标记
```

#### 连接失败显示内容：
- 通道编号和名称
- "VIDEO NOT CONNECTED" 英文提示
- "通道X: 视频未连接" 中文提示
- 视频源地址（如果太长会截断）
- 重连尝试次数信息
- "This channel will be skipped until restart" 状态提示

### 2. 自动清理功能

#### 主要特性：
- **定时检查**：每24小时检查一次是否需要清理
- **15天周期**：检测结果保存15天后自动清理
- **智能判断**：基于文件创建时间或上次清理时间判断
- **详细日志**：记录清理过程和结果

#### 清理逻辑：
1. **启动时检查**：系统启动时立即检查一次
2. **定时检查**：每24小时自动检查
3. **时间判断**：
   - 如果存在`.last_cleanup`文件，读取上次清理时间
   - 如果不存在，检查最旧文件的创建时间
   - 超过15天则触发清理
4. **执行清理**：
   - 删除所有检测结果文件（保留`.last_cleanup`标记文件）
   - 清理空的子文件夹
   - 更新清理时间标记
   - 记录清理统计信息

#### 清理统计信息：
- 删除的文件数量
- 释放的磁盘空间（MB）
- 清理完成时间

### 3. 界面状态更新

#### 通道状态显示：
- **运行中**：绿色文字，正常连接和检测
- **视频未连接**：红色文字，连接失败状态
- **初始化失败**：红色文字，线程启动失败

#### 状态栏消息：
- 显示自动清理完成信息
- 显示连接状态变化
- 显示系统操作结果

## 使用说明

### 启动系统
1. 运行 `multi_channel_area_alarm_detector_fixed.py`
2. 系统会自动尝试连接所有配置的视频源
3. 连接失败的通道会显示"视频未连接"状态
4. 连接成功的通道正常进行烟雾检测

### 查看连接状态
- 每个通道下方会显示当前状态
- 连接失败的通道会显示详细的失败信息
- 可以通过"重新初始化"按钮重新尝试连接

### 自动清理功能
- 无需手动操作，系统自动管理
- 可以在日志中查看清理记录
- 清理完成后状态栏会显示清理结果

## 配置说明

### 连接参数配置
```python
self.max_connection_attempts = 5  # 可修改最大重连次数
self.reconnect_delay = 2.0        # 可修改重连间隔
```

### 清理周期配置
```python
# 检查间隔（毫秒）
self.cleanup_timer.start(24 * 60 * 60 * 1000)  # 24小时

# 清理周期（天）
if (current_time - last_cleanup).days >= 15:  # 15天
```

## 日志记录

系统会记录以下信息：
- 视频连接尝试和结果
- 自动清理操作和统计
- 系统启动和关闭
- 错误和异常信息

日志文件：`16路烟雾检测日志.txt`

## 注意事项

1. **网络环境**：RTSP流连接受网络环境影响，建议确保网络稳定
2. **磁盘空间**：自动清理功能会释放磁盘空间，但请确保有足够空间存储15天的检测结果
3. **系统重启**：连接失败的通道需要重启系统或点击"重新初始化"才能重新尝试连接
4. **配置文件**：确保`video_paths.txt`中的视频源地址正确

## 故障排除

### 连接问题
- 检查网络连接
- 验证RTSP地址格式
- 确认摄像头设备状态
- 查看系统日志了解详细错误信息

### 清理问题
- 检查detection_results文件夹权限
- 确认磁盘空间充足
- 查看日志了解清理过程

## 技术实现

### 关键类和方法
- `AreaAlarmVideoThread.connect_video_source()` - 视频源连接
- `AreaAlarmVideoThread.reconnect_video_source()` - 重连逻辑
- `AreaAlarmVideoThread.show_connection_failed_frame()` - 失败状态显示
- `MainWindow.setup_auto_cleanup()` - 自动清理初始化
- `MainWindow.check_and_cleanup_results()` - 清理检查逻辑
- `MainWindow.cleanup_detection_results()` - 执行清理操作

### 依赖库
- `datetime` - 时间处理
- `shutil` - 文件操作
- `glob` - 文件匹配
- `QTimer` - 定时器功能

这些修改提高了系统的稳定性和可维护性，确保即使部分视频源连接失败，系统仍能正常运行其他通道的检测功能。
