#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
16路实时监控面积警报烟雾检测系统
基于gpu20250514(1)界面效果 + area_alarm_smoke_detector功能
16-Channel Real-time Area Alarm Smoke Detection System
"""
import logging
import os
import sys
import time
from datetime import datetime, timedelta
import threading
import concurrent.futures
import math
import shutil
import glob
# 添加CUDA路径
try:
    os.add_dll_directory(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin')
    os.add_dll_directory(r'E:\JSZK\DICHENG\opencv_contrib_cuda_4.6.0.20221106_win_amd64\install\x64\vc17\bin')
except:
    pass

import cv2
import numpy as np

# 导入pygame用于警报音
try:
    import pygame
    pygame.mixer.init()
    HAS_PYGAME = True
    print("✅ pygame音频支持已启用")
except ImportError:
    HAS_PYGAME = False
    print("⚠️ pygame未安装，将使用系统警报音")

# 导入PyQt5
from PyQt5.QtWidgets import (QApplication, QMainWindow, QLabel, QVBoxLayout,
                           QHBoxLayout, QPushButton, QWidget, QSizePolicy,
                           QComboBox, QScrollArea, QGridLayout, QTextEdit,
                           QMessageBox, QSpacerItem, QSpinBox)
from PyQt5.QtGui import QImage, QPixmap
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer


class GPUResourceManager:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
                    # 初始化GPU光流计算对象
                    cls._instance.gpu_flow = cv2.cuda_FarnebackOpticalFlow.create(
                        numLevels=3, pyrScale=0.5, fastPyramids=True,
                        winSize=15, numIters=3, polyN=5, polySigma=1.2, flags=0
                    )
        return cls._instance

    def calculate_flow(self, prev_frame, curr_frame):
        with self._lock:
            return self.gpu_flow.calc(prev_frame, curr_frame, None)
class ThreadPoolManager:
    def __init__(self, max_workers=4):
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        self.futures = {}

    def submit(self, task_id, task_fn, *args):
        if task_id in self.futures:
            self.futures[task_id].cancel()
        future = self.executor.submit(task_fn, *args)
        self.futures[task_id] = future
        return future

    def shutdown(self):
        self.executor.shutdown(wait=True)

def write_log(message):
    """写入日志文件"""
    current_directory = os.getcwd()
    file_name = "16路烟雾检测日志.txt"
    file_path = os.path.join(current_directory, file_name)
    
    try:
        with open(file_path, 'a', encoding='utf-8') as file:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            file.write(f"[{timestamp}] {message}\n")
    except Exception as e:
        print(f"Error writing to log file: {e}")


def read_video_paths_from_file(file_path):
    """从文件读取视频路径"""
    try:
        with open(file_path, 'r', encoding="utf-8") as file:
            paths = []
            for line in file:
                line = line.strip()
                if line:
                    paths.append(line)
            return paths
    except FileNotFoundError:
        print(f"文件 {file_path} 不存在")
        return []

class VideoProcessor:
    def __init__(self, video_path, thread_id, thread_name):
        self.video_path = video_path
        self.thread_id = thread_id
        self.thread_name = thread_name
        self._run_flag = True
        self.reset_state()
        self.gpu_manager = GPUResourceManager()

        # 异常图像输出文件夹
        self.output_dir = 'detection_results'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

        # 配置日志
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

        # 初始化报警声音
        pygame.mixer.init()
        self.alarm_sound = pygame.mixer.Sound('1.mp3') if os.path.exists('1.mp3') else None

    def reset_state(self):
        try:
            self.cap = cv2.VideoCapture(self.video_path)
            if not self.cap.isOpened():
                # 尝试作为数字索引打开
                try:
                    video_index = int(self.video_path)
                    self.cap = cv2.VideoCapture(video_index)
                except ValueError:
                    pass


            if not self.cap.isOpened():
                logging.error(f"无法打开视频源: {self.video_path}")
                return

            # 设置硬件加速解码
            #self.cap.set(cv2.CAP_PROP_HW_ACCELERATION, cv2.VIDEO_ACCELERATION_ANY)
        except Exception as e:
            logging.error(f"初始化视频捕获失败: {str(e)}")

        self.prev_gray = None
        self.frame_counter = 0
        self.detection_interval = 25
        self.contour_threshold = 150
        self.threshold_flow = 1.5
        self.min_area = 500
        self.alarm_triggered = False
        self.stop_detection_flag = False
        self.param_lock = threading.Lock()
        self.kernel = np.ones((5, 5), np.uint8)
        self.gpu_prev_frame = None

    def process_frame(self):
        if not self._run_flag or not self.cap or not self.cap.isOpened():
            return None, False

        try:
            ret, frame = self.cap.read()
            if not ret:
                # 尝试重新打开视频
                self.cap.release()
                time.sleep(1)
                self.reset_state()
                return None, False

            # 降低分辨率以节省资源
           # frame = cv2.resize(frame, (1920, 1080))
            frame = cv2.resize(frame, (640, 360))

            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            if self.prev_gray is None:
                self.prev_gray = gray
                self.gpu_prev_frame = cv2.cuda_GpuMat()
                self.gpu_prev_frame.upload(gray)
                return frame, False

            self.frame_counter += 1

            # 跳过非检测帧
            if self.frame_counter % self.detection_interval != 0:
                return frame, False

            # 上传当前帧到GPU
            gpu_frame = cv2.cuda_GpuMat()
            gpu_frame.upload(gray)

            # 计算光流
            gpu_flow = self.gpu_manager.calculate_flow(self.gpu_prev_frame, gpu_frame)
            flow = gpu_flow.download()
            magnitude, _ = cv2.cartToPolar(flow[..., 0], flow[..., 1])

            # 阈值处理
            mask = np.zeros_like(magnitude)
            mask[magnitude > self.threshold_flow] = 255
            mask = mask.astype(np.uint8)

            # 形态学处理
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, self.kernel)

            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            with self.param_lock:
                contour_threshold = self.contour_threshold

            # 检查是否需要触发警报
            if not self.stop_detection_flag:
                self.alarm_triggered = len(contours) > contour_threshold

                if self.alarm_triggered:
                    # 播放警报声音
                    if self.alarm_sound:
                        self.alarm_sound.play()

                    # 记录日志
                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    log_message = f"{self.thread_name}在 {current_time} 检测到烟雾"
                    self.write_log_to_file(log_message)

                    # 在帧上绘制检测结果
                    for contour in contours:
                        if cv2.contourArea(contour) > self.min_area:
                            x, y, w, h = cv2.boundingRect(contour)
                            cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 0, 255), 2)  # 红色框
                            # cv2.putText(frame, "SMOKE DETECTED", (x, y - 10),
                            #             cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)  # 红色文字


                    # 获取当前日期和时间
                    now = datetime.now().strftime('%Y-%m-%d-%H-%M-%S')
                    # 保存原始图像
                    output_img = now + '.jpg'
                    # thread_name_encoded = self.thread_name.encode('utf-8')
                    original_image_path = os.path.join(self.output_dir,
                                                       f'{self.thread_name}_detected_img_{output_img}')

                    cv2.imencode('.jpg', frame)[1].tofile(original_image_path)

            # 更新前一帧
            self.prev_gray = gray
            if self.gpu_prev_frame is not None:
                self.gpu_prev_frame.release()
            self.gpu_prev_frame = gpu_frame

            return frame, self.alarm_triggered

        except Exception as e:
            logging.error(f"处理帧时出错: {str(e)}")
            return None, False

    def update_params(self, detection_interval, contour_threshold):
        with self.param_lock:
            self.detection_interval = detection_interval
            self.contour_threshold = contour_threshold

    def stop(self):
        self._run_flag = False
        if hasattr(self, 'cap') and self.cap is not None:
            self.cap.release()
        if hasattr(self, 'gpu_prev_frame') and self.gpu_prev_frame is not None:
            self.gpu_prev_frame.release()
        if hasattr(self, 'alarm_sound') and self.alarm_sound:
            self.alarm_sound.stop()

    def mute_alarm(self):
        self.stop_detection_flag = True
        if self.alarm_triggered and self.alarm_sound:
            self.alarm_sound.stop()
            self.alarm_triggered = False

    def resume_detection(self):
        self.stop_detection_flag = False

    @staticmethod
    def write_log_to_file(message):
        try:
            current_directory = os.getcwd()
            file_name = "漏袋检测日志.txt"
            file_path = os.path.join(current_directory, file_name)
            with open(file_path, 'a', encoding='utf-8') as file:
                file.write(message + '\n')
        except Exception as e:
            logging.error(f"写入日志文件错误: {e}")

class SquareLabel(QLabel):
    """保持16:9比例的视频显示标签"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setMinimumSize(1, 1)
    
    def resizeEvent(self, event):
        super().resizeEvent(event)
        width = self.width()
        height = int(width * 9 / 16)
        self.setFixedHeight(height)


class AreaAlarmVideoThread(QThread):
    """面积警报视频处理线程"""

    change_pixmap_signal = pyqtSignal(np.ndarray)
    log_signal = pyqtSignal(str)
    alarm_signal = pyqtSignal(int, float)  # 通道号, 面积
    alarm_status_signal = pyqtSignal(int, str, bool)  # 通道号, 通道名称, 是否报警
    
    def __init__(self, video_path, thread_id, thread_name):
        super().__init__()
        self.video_path = video_path
        self.thread_id = thread_id
        self.thread_name = thread_name
        self._run_flag = True
        self.stop_detection_flag = False
        self.alarm_triggered = False
        
        # 检测视频源类型
        self.is_local_video = self.is_local_video_file(video_path)

        # 初始化视频捕获
        self.cap = None
        self.connection_attempts = 0
        self.max_connection_attempts = 5
        self.reconnect_delay = 2.0
        self.connection_failed = False  # 标记连接是否彻底失败

        # 尝试连接视频源
        if not self.connect_video_source():
            print(f"⚠️ 通道{thread_id}无法连接视频源: {video_path}")
            self.connection_failed = True
            # 即使连接失败也继续初始化，但标记为失败状态
            self.video_width = 640
            self.video_height = 480
            print(f"⚠️ 通道{thread_id}连接失败，跳过该通道，使用默认分辨率: {self.video_width}x{self.video_height}")
        else:
            print(f"✅ 通道{thread_id}视频源连接成功")
            self.connection_failed = False
            # 获取视频信息
            if self.cap and self.cap.isOpened():
                self.video_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                self.video_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            else:
                self.video_width = 640
                self.video_height = 480

        # 本地视频特有属性
        if self.is_local_video:
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            self.current_frame = 0
            print(f"📹 本地视频信息: {self.total_frames}帧, {self.fps:.2f}fps")
        else:
            self.total_frames = 0
            self.fps = 30.0  # 默认FPS
            self.current_frame = 0
        
        # 面积阈值设置
        self.area_threshold_cm2 = 50  # 5平方厘米
        self.area_threshold_pixels = self.calculate_pixel_area_threshold()
        
        # 检测参数
        self.brightness_min = 80
        self.brightness_max = 180
        self.min_area = 500
        self.max_area = 50000
        self.detection_interval = 25  # 检测间隔
        self.contour_threshold = 800   # 轮廓阈值
        
        # 状态变量
        self.frame_count = 0
        self.prev_gray = None
        self.last_alarm_time = 0
        self.alarm_cooldown = 3.0

        # 警报状态控制
        self.is_alarm_playing = False  # 当前是否正在播放警报音
        self.alarm_start_time = 0      # 警报开始时间
        self.max_alarm_duration = 5.0  # 最大警报持续时间（5秒）

        # 帧历史缓存（用于后3帧比较逻辑）
        self.frame_history = []  # 存储最近4帧的烟雾浓度数据
        self.max_history_frames = 4  # 保存最近4帧（当前帧+后3帧）

        # 全局报警音控制（防止多通道同时播放）
        self.global_alarm_playing = False
        
        # 时间标记区域设置（忽略左上角时间）
        self.time_region = self.calculate_time_region()
        
        # 创建输出目录
        self.output_dir = f'detection_results/channel_{thread_id}'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        
        # 警报音
        self.alarm_sound = None
        if HAS_PYGAME and os.path.exists('1.mp3'):
            try:
                self.alarm_sound = pygame.mixer.Sound('1.mp3')
                print(f"✅ 通道{thread_id}警报音加载成功")
            except Exception as e:
                print(f"⚠️ 通道{thread_id}警报音加载失败: {e}")
        
        print(f"✅ 通道{thread_id}({thread_name})初始化完成")

    def is_local_video_file(self, video_path):
        """检测是否为本地视频文件"""
        # 检查是否为本地文件路径
        if os.path.exists(video_path):
            return True

        # 检查文件扩展名
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.m4v', '.3gp']
        _, ext = os.path.splitext(video_path.lower())
        if ext in video_extensions:
            return True

        # 检查是否为网络流
        if video_path.startswith(('rtsp://', 'rtmp://', 'http://', 'https://')):
            return False

        return False

    def connect_video_source(self):
        """连接视频源"""
        self.connection_attempts += 1

        try:
            if self.is_local_video:
                print(f"📹 通道{self.thread_id}连接本地视频: {self.video_path}")
            else:
                print(f"🌐 通道{self.thread_id}连接RTSP流: {self.video_path} (尝试 {self.connection_attempts}/{self.max_connection_attempts})")

            # 创建VideoCapture对象
            self.cap = cv2.VideoCapture(self.video_path)

            # 设置RTSP连接参数（针对海康设备优化）
            if not self.is_local_video:
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)        # 减少缓冲，提高实时性
                self.cap.set(cv2.CAP_PROP_FPS, 25)              # 设置FPS
                self.cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('H', '2', '6', '4'))  # 指定编码格式
                # 海康RTSP优化参数
                self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)    # 适中分辨率，平衡性能和质量
                self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
                # 设置超时参数（如果OpenCV版本支持）
                try:
                    self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)   # 连接超时5秒
                    self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 3000)   # 读取超时3秒
                except:
                    pass  # 旧版本OpenCV可能不支持这些参数

            # 检查连接是否成功
            if self.cap.isOpened():
                # 尝试读取一帧来验证连接
                ret, frame = self.cap.read()
                if ret and frame is not None:
                    # 获取视频信息
                    self.video_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    self.video_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

                    if self.video_width > 0 and self.video_height > 0:
                        if self.is_local_video:
                            print(f"✅ 通道{self.thread_id}本地视频连接成功: {self.video_width}x{self.video_height}")
                        else:
                            print(f"✅ 通道{self.thread_id}RTSP流连接成功: {self.video_width}x{self.video_height}")

                        self.connection_attempts = 0  # 重置连接尝试次数
                        self.connection_failed = False
                        return True
                    else:
                        print(f"⚠️ 通道{self.thread_id}视频尺寸无效: {self.video_width}x{self.video_height}")
                else:
                    print(f"⚠️ 通道{self.thread_id}无法读取视频帧")
            else:
                print(f"⚠️ 通道{self.thread_id}无法打开视频源")

        except Exception as e:
            print(f"⚠️ 通道{self.thread_id}连接异常: {e}")

        # 连接失败，清理资源
        if self.cap:
            self.cap.release()
            self.cap = None

        # 检查是否达到最大重连次数
        if self.connection_attempts >= self.max_connection_attempts:
            print(f"❌ 通道{self.thread_id}达到最大连接尝试次数({self.max_connection_attempts})，标记为连接失败")
            self.connection_failed = True

        return False

    def reconnect_video_source(self):
        """重新连接视频源"""
        if self.connection_attempts >= self.max_connection_attempts:
            print(f"❌ 通道{self.thread_id}达到最大重连次数({self.max_connection_attempts})，停止尝试，跳过该通道")
            self.connection_failed = True
            return False

        print(f"🔄 通道{self.thread_id}尝试重新连接... (尝试 {self.connection_attempts + 1}/{self.max_connection_attempts})")
        time.sleep(self.reconnect_delay)

        success = self.connect_video_source()
        if not success and self.connection_attempts >= self.max_connection_attempts:
            print(f"❌ 通道{self.thread_id}重连失败，已达到最大尝试次数，跳过该通道")
            self.connection_failed = True

        return success

    def calculate_pixel_area_threshold(self):
        """计算5平方厘米对应的像素面积"""
        # 假设视频覆盖区域：100cm × 60cm = 6000cm²
        total_area_cm2 = 100 * 60
        total_pixels = self.video_width * self.video_height
        pixels_per_cm2 = total_pixels / total_area_cm2
        threshold_pixels = int(self.area_threshold_cm2 * pixels_per_cm2)
        
        print(f"📐 通道{self.thread_id}面积换算: 50cm² = {threshold_pixels}像素")
        return threshold_pixels
    
    def calculate_time_region(self):
        """计算时间标记区域（左上角）"""
        time_width = int(self.video_width * 0.25)
        time_height = int(self.video_height * 0.08)
        return {
            'x': 0,
            'y': 0,
            'width': time_width,
            'height': time_height
        }
    
    def mask_time_region(self, mask):
        """在检测掩码中屏蔽时间区域"""
        time_region = self.time_region
        mask[time_region['y']:time_region['y'] + time_region['height'],
             time_region['x']:time_region['x'] + time_region['width']] = 0
        return mask
    
    def detect_smoke(self, frame):
        """烟雾检测算法（整合面积检测）"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # 1. 亮度过滤
            brightness_mask = cv2.inRange(gray, self.brightness_min, self.brightness_max)

            # 2. 运动检测
            motion_mask = np.ones_like(gray) * 255
            if self.prev_gray is not None:
                diff = cv2.absdiff(gray, self.prev_gray)
                motion_mask = cv2.threshold(diff, 20, 255, cv2.THRESH_BINARY)[1]

                kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
                motion_mask = cv2.morphologyEx(motion_mask, cv2.MORPH_CLOSE, kernel)

            # 3. 结合亮度和运动
            combined_mask = cv2.bitwise_and(brightness_mask, motion_mask)

            # 4. 屏蔽时间标记区域
            combined_mask = self.mask_time_region(combined_mask)

            # 5. 形态学处理
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)

            # 6. 轮廓检测
            contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 7. 分析轮廓并计算总面积
            smoke_regions = []
            total_area = 0

            for contour in contours:
                area = cv2.contourArea(contour)
                if self.min_area < area < self.max_area:
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h
                    if 0.3 < aspect_ratio < 3.0:
                        confidence = min(area / 10000, 1.0)
                        smoke_regions.append({
                            'bbox': (x, y, w, h),
                            'area': area,
                            'confidence': confidence,
                            'contour': contour
                        })
                        total_area += area

            # 8. 计算烟雾浓度（基于总面积和轮廓数量）
            smoke_density = self.calculate_smoke_density(total_area, len(smoke_regions))

            # 9. 更新帧历史缓存
            self.update_frame_history(smoke_density)

            self.prev_gray = gray.copy()
            return smoke_regions, combined_mask, total_area

        except Exception as e:
            print(f"⚠️ 通道{self.thread_id}检测出错: {e}")
            return [], np.zeros_like(frame[:,:,0]), 0

    def calculate_smoke_density(self, total_area, contour_count):
        """计算烟雾浓度"""
        # 综合考虑面积和轮廓数量来计算烟雾浓度
        area_density = total_area / (self.video_width * self.video_height)  # 面积密度
        contour_density = contour_count / 100.0  # 轮廓密度

        # 加权计算总烟雾浓度
        smoke_density = (area_density * 0.7 + contour_density * 0.3) * 100
        return smoke_density

    def update_frame_history(self, smoke_density):
        """更新帧历史缓存"""
        # 添加当前帧的烟雾浓度
        self.frame_history.append(smoke_density)

        # 保持历史帧数量不超过最大值
        if len(self.frame_history) > self.max_history_frames:
            self.frame_history.pop(0)  # 移除最旧的帧
    
    def check_area_alarm(self, total_area):
        """检查是否需要触发面积警报（基于后3帧比较逻辑）"""
        current_time = time.time()

        # 检查是否需要停止当前播放的警报音（5秒后自动停止）
        if self.is_alarm_playing:
            if current_time - self.alarm_start_time >= self.max_alarm_duration:
                self.stop_alarm_sound()
                print(f"⏰ 通道{self.thread_id}警报音已达到5秒持续时间，自动停止")
                return False

        # 检查是否有足够的历史帧进行比较（需要至少4帧：当前帧+后3帧）
        if len(self.frame_history) < self.max_history_frames:
            return False

        # 获取当前帧和后3帧的烟雾浓度
        current_density = self.frame_history[-1]  # 当前帧（最新的）
        prev_3_densities = self.frame_history[-4:-1]  # 后3帧

        # 检查后3帧的烟雾浓度是否均比当前帧大
        all_prev_greater = all(prev_density > current_density for prev_density in prev_3_densities)

        # 同时检查面积阈值
        area_threshold_met = total_area >= self.area_threshold_pixels

        if all_prev_greater and area_threshold_met:
            # 如果当前没有在播放警报音，且满足冷却时间，才触发新的警报
            if not self.is_alarm_playing and not self.global_alarm_playing:
                if current_time - self.last_alarm_time >= self.alarm_cooldown:
                    self.trigger_alarm(total_area, current_density, prev_3_densities)
                    self.last_alarm_time = current_time
                    return True

        return False
    
    def trigger_alarm(self, total_area, current_density, prev_3_densities):
        """触发警报"""
        current_time = time.time()
        self.alarm_triggered = True
        self.is_alarm_playing = True
        self.global_alarm_playing = True  # 设置全局报警状态
        self.alarm_start_time = current_time

        area_cm2 = total_area * self.area_threshold_cm2 / self.area_threshold_pixels

        # 记录详细的日志信息
        prev_densities_str = ", ".join([f"{d:.2f}" for d in prev_3_densities])
        log_message = (f"🚨 通道{self.thread_id}({self.thread_name}) 烟雾警报! "
                      f"检测面积: {area_cm2:.2f}cm² (阈值: {self.area_threshold_cm2}cm²) "
                      f"当前烟雾浓度: {current_density:.2f}, 后3帧浓度: [{prev_densities_str}]")
        print(log_message)
        self.log_signal.emit(log_message)
        write_log(log_message)

        # 发送警报信号
        self.alarm_signal.emit(self.thread_id, area_cm2)

        # 发送界面状态更新信号
        self.alarm_status_signal.emit(self.thread_id, self.thread_name, True)

        # 播放警报音（只有在没有全局播放时才播放）
        if self.alarm_sound and not self.stop_detection_flag and not self.is_alarm_playing_audio():
            try:
                self.alarm_sound.play(-1)  # 循环播放
                print(f"🔊 通道{self.thread_id}开始播放警报音（持续5秒）")
            except Exception as e:
                print(f"⚠️ 通道{self.thread_id}播放警报音失败: {e}")
                self.is_alarm_playing = False
                self.global_alarm_playing = False

    def is_alarm_playing_audio(self):
        """检查警报音是否正在播放"""
        if HAS_PYGAME and self.alarm_sound:
            try:
                # 检查pygame mixer是否正在播放声音
                return pygame.mixer.get_busy()
            except:
                return False
        return False

    def stop_alarm_sound(self):
        """停止警报音"""
        self.is_alarm_playing = False
        self.alarm_triggered = False
        self.global_alarm_playing = False  # 重置全局报警状态

        # 发送界面状态更新信号（停止报警）
        self.alarm_status_signal.emit(self.thread_id, self.thread_name, False)

        if HAS_PYGAME and self.alarm_sound:
            try:
                # 只停止这个通道的声音
                pygame.mixer.stop()
                print(f"🔇 通道{self.thread_id}警报音已停止")
            except Exception as e:
                print(f"⚠️ 通道{self.thread_id}停止警报音失败: {e}")
    
    def draw_results(self, frame, smoke_regions, total_area):
        """绘制检测结果"""
        result_frame = frame.copy()
        
        # 计算实际面积
        area_cm2 = total_area * self.area_threshold_cm2 / self.area_threshold_pixels
        
        # 绘制时间区域标记
        time_region = self.time_region
        cv2.rectangle(result_frame,
                     (time_region['x'], time_region['y']),
                     (time_region['x'] + time_region['width'], 
                      time_region['y'] + time_region['height']),
                     (128, 128, 128), 1)
        cv2.putText(result_frame, "TIME IGNORED",
                   (time_region['x'] + 5, time_region['y'] + 15),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (128, 128, 128), 1)
        
        # 绘制检测区域
        for region in smoke_regions:
            x, y, w, h = region['bbox']
            area = region['area']
            
            # 根据面积选择颜色
            if total_area >= self.area_threshold_pixels:
                color = (0, 0, 255)  # 红色 - 警报
                thickness = 3
            elif region['confidence'] > 0.7:
                color = (0, 165, 255)  # 橙色 - 高置信度
                thickness = 2
            else:
                color = (0, 255, 255)  # 黄色 - 低置信度
                thickness = 2
            
            cv2.rectangle(result_frame, (x, y), (x + w, y + h), color, thickness)
            
            # 绘制区域面积
            region_cm2 = area * self.area_threshold_cm2 / self.area_threshold_pixels
            label = f"Area: {region_cm2:.1f}cm²"
            cv2.putText(result_frame, label, (x, y - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        # 绘制总面积信息
        total_text = f"Total: {area_cm2:.2f}cm² / {self.area_threshold_cm2}cm²"
        if total_area >= self.area_threshold_pixels:
            total_color = (0, 0, 255)  # 红色
            if self.is_alarm_playing:
                status_text = "ALARM PLAYING!"
            else:
                status_text = "ALARM DETECTED!"
        else:
            total_color = (0, 255, 0)  # 绿色
            status_text = "Normal"
        
        # cv2.putText(result_frame, total_text, (10, 30),
        #            cv2.FONT_HERSHEY_SIMPLEX, 0.6, total_color, 2)
        # cv2.putText(result_frame, status_text, (10, 60),
        #            cv2.FONT_HERSHEY_SIMPLEX, 0.8, total_color, 2)
        
        # 绘制通道信息
        if self.is_local_video:
            # 本地视频显示播放进度
            progress = (self.current_frame / self.total_frames * 100) if self.total_frames > 0 else 0
            channel_text = f"CH{self.thread_id}: {self.thread_name} [{progress:.1f}%]"
        else:
            # 网络流显示帧数
            channel_text = f"CH{self.thread_id}: {self.thread_name} [Frame:{self.frame_count}]"

        cv2.putText(result_frame, channel_text, (10, self.video_height - 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # 本地视频额外显示循环标识
        if self.is_local_video:
            loop_text = f"LOCAL VIDEO (Loop)"
            cv2.putText(result_frame, loop_text, (10, self.video_height - 40),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
        
        # 绘制停止警报按钮（如果警报激活）
        if self.alarm_triggered:
            button_x = self.video_width - 120
            button_y = 10
            button_w = 110
            button_h = 30
            
            cv2.rectangle(result_frame, (button_x, button_y), 
                         (button_x + button_w, button_y + button_h),
                         (0, 0, 255), -1)
            cv2.rectangle(result_frame, (button_x, button_y), 
                         (button_x + button_w, button_y + button_h),
                         (255, 255, 255), 2)
            cv2.putText(result_frame, "STOP ALARM", (button_x + 5, button_y + 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        return result_frame

    def save_alarm_image(self, frame, total_area):
        """保存警报图像到detection_results文件夹"""
        # 确保detection_results文件夹存在
        main_output_dir = 'detection_results'
        if not os.path.exists(main_output_dir):
            os.makedirs(main_output_dir)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        area_cm2 = total_area * self.area_threshold_cm2 / self.area_threshold_pixels

        filename = f"ALARM_CH{self.thread_id+1}_{timestamp}_area_{area_cm2:.2f}cm2.jpg"
        filepath = os.path.join(main_output_dir, filename)

        # 在图像上添加警报信息
        alarm_frame = frame.copy()
        alarm_text = f"SMOKE ALARM CH{self.thread_id+1} - Area: {area_cm2:.2f}cm²"
        time_text = f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # 添加警报信息到图像上
        cv2.putText(alarm_frame, alarm_text, (50, 100),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 255), 3)
        cv2.putText(alarm_frame, time_text, (50, 140),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 2)

        cv2.imwrite(filepath, alarm_frame)
        print(f"💾 通道{self.thread_id+1}警报图像已保存到detection_results文件夹: {filename}")

        return filepath

    def update_params(self, detection_interval=None, contour_threshold=None,
                     area_threshold=None, brightness_min=None, brightness_max=None):
        """更新检测参数"""
        if detection_interval is not None:
            self.detection_interval = detection_interval
        if contour_threshold is not None:
            self.contour_threshold = contour_threshold
        if area_threshold is not None:
            self.area_threshold_cm2 = area_threshold
            self.area_threshold_pixels = self.calculate_pixel_area_threshold()
        if brightness_min is not None:
            self.brightness_min = brightness_min
        if brightness_max is not None:
            self.brightness_max = brightness_max

    def run(self):
        """主运行循环"""
        # 如果初始化时连接失败，检查是否彻底失败
        if self.connection_failed:
            print(f"❌ 通道{self.thread_id}连接彻底失败，跳过该通道，显示未连接状态")
            self.show_connection_failed_frame()
            return

        # 如果初始化时连接失败，在这里重新尝试连接
        if self.cap is None or not self.cap.isOpened():
            print(f"🔄 通道{self.thread_id}尝试重新连接视频源...")
            if not self.connect_video_source():
                if self.connection_failed:
                    print(f"❌ 通道{self.thread_id}连接彻底失败，跳过该通道，显示未连接状态")
                    self.show_connection_failed_frame()
                    return
                else:
                    print(f"❌ 通道{self.thread_id}连接失败，线程退出")
                    return

        while self._run_flag:
            if self.cap is None or not self.cap.isOpened():
                if self.connection_failed:
                    # 如果已经标记为连接失败，显示未连接状态
                    print(f"❌ 通道{self.thread_id}已标记为连接失败，显示未连接状态")
                    self.show_connection_failed_frame()
                    break

                print(f"⚠️ 通道{self.thread_id}视频源未连接，尝试重新连接...")
                if not self.reconnect_video_source():
                    if self.connection_failed:
                        print(f"❌ 通道{self.thread_id}达到最大重连次数，跳过该通道")
                        self.show_connection_failed_frame()
                        break
                    else:
                        print(f"❌ 通道{self.thread_id}重连失败，线程退出")
                        break
                continue

            ret, frame = self.cap.read()
            if not ret:
                if self.is_local_video:
                    # 本地视频播放完毕，重新开始循环播放
                    print(f"🔄 通道{self.thread_id}本地视频播放完毕，重新开始循环播放")
                    if self.cap and self.cap.isOpened():
                        self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 重置到第一帧
                        self.current_frame = 0
                        ret, frame = self.cap.read()
                        if not ret:
                            print(f"⚠️ 通道{self.thread_id}无法重新读取视频")
                            break
                    else:
                        print(f"⚠️ 通道{self.thread_id}视频源已断开")
                        break
                else:
                    # RTSP流重新连接
                    print(f"🔄 通道{self.thread_id}RTSP流断开，尝试重新连接...")
                    if self.cap:
                        self.cap.release()
                        self.cap = None

                    # 尝试重新连接
                    if self.reconnect_video_source():
                        print(f"✅ 通道{self.thread_id}重新连接成功")
                        continue
                    else:
                        if self.connection_failed:
                            print(f"❌ 通道{self.thread_id}达到最大重连次数，跳过该通道")
                            self.show_connection_failed_frame()
                            break
                        else:
                            print(f"❌ 通道{self.thread_id}重新连接失败")
                            break

            self.frame_count += 1
            if self.is_local_video:
                self.current_frame += 1

            # 根据检测间隔进行检测
            if self.frame_count % self.detection_interval == 0 and not self.stop_detection_flag:
                try:
                    # 检测烟雾
                    smoke_regions, _, total_area = self.detect_smoke(frame)

                    # 检查面积警报
                    alarm_triggered = self.check_area_alarm(total_area)

                    # 绘制结果
                    result_frame = self.draw_results(frame, smoke_regions, total_area)

                    # 如果触发警报，保存图像
                    if alarm_triggered:
                        self.save_alarm_image(result_frame, total_area)

                    # 发送帧信号
                    self.change_pixmap_signal.emit(result_frame)

                except Exception as e:
                    print(f"⚠️ 通道{self.thread_id}处理帧出错: {e}")
                    self.change_pixmap_signal.emit(frame)
            else:
                self.change_pixmap_signal.emit(frame)

            # 根据视频类型调整播放速度
            if self.is_local_video and self.fps > 0:
                time.sleep(1.0 / self.fps)  # 按原始FPS播放
            else:
                time.sleep(0.025)  # 约30fps

    def show_connection_failed_frame(self):
        """显示连接失败的帧"""
        print(f"📺 通道{self.thread_id}开始显示连接失败状态")

        while self._run_flag:
            # 创建一个深灰色背景的图像
            failed_frame = np.zeros((self.video_height, self.video_width, 3), dtype=np.uint8)
            failed_frame[:] = (40, 40, 40)  # 深灰色背景

            # 添加"视频未连接"文字
            text = "VIDEO NOT CONNECTED"
            text_cn = f"通道{self.thread_id+1}: 视频未连接"

            # 计算文字位置（居中）
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 1.0
            thickness = 2

            # 英文文字
            text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
            text_x = (self.video_width - text_size[0]) // 2
            text_y = (self.video_height - text_size[1]) // 2
            cv2.putText(failed_frame, text, (text_x, text_y), font, font_scale, (0, 0, 255), thickness)

            # 中文文字
            text_cn_size = cv2.getTextSize(text_cn, font, 0.8, thickness)[0]
            text_cn_x = (self.video_width - text_cn_size[0]) // 2
            text_cn_y = text_y + 50
            cv2.putText(failed_frame, text_cn, (text_cn_x, text_cn_y), font, 0.8, (0, 255, 255), thickness)

            # 添加通道信息
            channel_info = f"Channel {self.thread_id+1}: {self.thread_name}"
            cv2.putText(failed_frame, channel_info, (10, 30), font, 0.6, (255, 255, 255), 1)

            # 添加视频源信息
            source_info = f"Source: {self.video_path}"
            if len(source_info) > 50:  # 如果路径太长，截断显示
                source_info = source_info[:47] + "..."
            cv2.putText(failed_frame, source_info, (10, 60), font, 0.4, (200, 200, 200), 1)

            # 添加重连提示
            retry_text = f"Failed after {self.max_connection_attempts} attempts - Channel skipped"
            cv2.putText(failed_frame, retry_text, (10, self.video_height - 50), font, 0.4, (128, 128, 128), 1)

            # 添加状态提示
            status_text = "This channel will be skipped until restart"
            cv2.putText(failed_frame, status_text, (10, self.video_height - 30), font, 0.4, (255, 165, 0), 1)

            # 发送帧信号
            self.change_pixmap_signal.emit(failed_frame)

            # 等待一段时间再更新
            time.sleep(1.0)

    def mute_alarm(self):
        """静音警报"""
        self.stop_detection_flag = True
        if self.alarm_triggered and self.alarm_sound:
            self.stop_alarm_sound()

    def resume_detection(self):
        """恢复检测"""
        self.stop_detection_flag = False

    def stop(self):
        """停止线程"""
        self._run_flag = False
        self.stop_detection_flag = True

        # 停止警报音和清理警报状态
        self.stop_alarm_sound()

        if hasattr(self, 'cap') and self.cap and self.cap.isOpened():
            self.cap.release()

        self.quit()
        self.wait()


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('烟雾检测系统 V1.0')
        self.setGeometry(100, 100, 1200, 800)  # 增加窗口高度
        self.log_window = None
        self.txt_file_path = 'video_paths.txt'
        self.output_dir = 'detection_results'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

        # 初始化线程池
        self.thread_pool = ThreadPoolManager(max_workers=4)

        # 初始化GPU资源管理器
        self.gpu_manager = GPUResourceManager()

        # 初始化自动清理功能
        self.setup_auto_cleanup()

        # 初始化连接状态监控
        self.setup_connection_monitor()

        # 初始化UI
        self.initUI()

    def initUI(self):
        self.txt_file_path = 'video_paths.txt'
        self.read_video_paths = read_video_paths_from_file(self.txt_file_path)

        # 创建处理器和更新器列表
        self.processors = []
        self.updaters = []

        # 计算视频流数量
        num_streams = int(len(self.read_video_paths) / 2)
        self.image_label = [None] * num_streams
        self.number_label = [None] * num_streams

        self.setStyleSheet("""
            QMainWindow {
                background-color: #2c3e50;
            }
            QPushButton, QTextEdit, QSpinBox, QComboBox, QLabel {
                font-size: 14pt;
                font-family: "微软雅黑";
                padding: 8px 16px;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 5px;

            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
            QComboBox, QSpinBox {
                background-color: white;
                color: #000000;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
            }
            QLabel#titleLabel {
                font-size: 48px;
                font-weight: bold;
                color: #ecf0f1;
                padding: 20px;
            }
        """)

        # 主布局
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # 标题布局
        title_layout = QHBoxLayout()
        title_layout.addStretch(1)

        # 标题图片
        title_image = QLabel()
        if os.path.exists('title.png'):
            pixmap = QPixmap('title.png')
            scaled_pixmap = pixmap.scaled(80, 80, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            title_image.setPixmap(scaled_pixmap)
        else:
            title_image.setText("LOGO")
            title_image.setStyleSheet("font-size: 24px; color: white;")

        # 标题文字
        title_label = QLabel('烟雾检测系统')
        title_label.setObjectName("titleLabel")

        title_layout.addWidget(title_image)
        title_layout.addWidget(title_label)
        title_layout.addStretch(1)

        main_layout.addLayout(title_layout)

        # 视频网格布局
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("background-color: #34495e; border: none;")

        video_container = QWidget()
        video_layout = QGridLayout(video_container)
        video_layout.setSpacing(15)

        # 计算行数和列数 (每行最多4个视频)
        cols =4
        rows = math.ceil(num_streams / cols)

        for i in range(num_streams):
            row = i // cols
            col = i % cols

            # 创建容器部件
            video_widget = QWidget()
            video_widget.setStyleSheet("background-color: #2c3e50; border-radius: 8px;")
            video_box = QVBoxLayout(video_widget)
            video_box.setContentsMargins(5, 5, 5, 5)
            video_box.setSpacing(5)

            # 视频编号
            number_label = QLabel(f"监控点 {i + 1}: {self.read_video_paths[i * 2 + 1]}")
            number_label.setStyleSheet("""
                QLabel {
                    color: #ecf0f1;
                    font-size: 16px;
                    font-weight: bold;
                    padding: 5px;
                }
            """)
            video_box.addWidget(number_label)

            # 视频显示区域
            image_label = SquareLabel()
            self.image_label[i] = image_label
            video_box.addWidget(image_label, 1)

            # 状态指示器
            status_label = QLabel("状态: 未启动")
            status_label.setStyleSheet("""
                QLabel {
                    color: #bdc3c7;
                    font-size: 14px;
                    padding: 5px;
                }
            """)
            self.number_label[i] = status_label
            video_box.addWidget(status_label)

            video_layout.addWidget(video_widget, row, col)

        scroll_area.setWidget(video_container)
        main_layout.addWidget(scroll_area, 1)  # 添加伸缩因子

        # 控制按钮布局
        control_layout = QHBoxLayout()
        control_layout.setSpacing(15)

        # 左侧控制按钮
        left_control = QHBoxLayout()
        self.detect_button = QPushButton('开始检测')
        self.detect_button.clicked.connect(self.start_detection)
        self.detect_button.setStyleSheet("background-color: #2ecc71;")

        self.stop_button = QPushButton('停止报警')
        self.stop_button.clicked.connect(self.stop_detection)
        self.stop_button.setStyleSheet("background-color: #e74c3c;")

        self.alarm_button = QPushButton('查看报警图片')
        self.alarm_button.clicked.connect(self.open_alarm_folder)
        self.alarm_button.setStyleSheet("background-color: #f39c12;")

        left_control.addWidget(self.detect_button)
        left_control.addWidget(self.stop_button)
        left_control.addWidget(self.alarm_button)

        # 中间控制
        center_control = QHBoxLayout()
        self.comboBox = QComboBox()
        self.comboBox.addItem("选择监控点...")
        for i in range(num_streams):
            self.comboBox.addItem(f"{i + 1}号: {self.read_video_paths[i * 2 + 1]}")

        self.select_stop_button = QPushButton('暂停报警')
        self.select_stop_button.clicked.connect(self.on_select_stop)

        center_control.addWidget(self.comboBox)
        center_control.addWidget(self.select_stop_button)

        # 右侧控制
        right_control = QHBoxLayout()
        self.add_video_button = QPushButton('管理信号源')
        self.add_video_button.clicked.connect(self.open_video_paths)

        self.init_button = QPushButton('重新初始化')
        self.init_button.clicked.connect(self.initialize_monitoring)

        self.log_button = QPushButton('查看日志')
        self.log_button.clicked.connect(self.show_log_window)

        self.exit_button = QPushButton('退出系统')
        self.exit_button.clicked.connect(self.exit_program)
        self.exit_button.setStyleSheet("background-color: #7f8c8d;")

        right_control.addWidget(self.add_video_button)
        right_control.addWidget(self.init_button)
        right_control.addWidget(self.log_button)
        right_control.addWidget(self.exit_button)

        # 添加到主控制布局
        control_layout.addLayout(left_control, 1)
        control_layout.addLayout(center_control, 2)
        control_layout.addLayout(right_control, 1)

        main_layout.addLayout(control_layout)

        # 参数调整布局
        param_layout = QHBoxLayout()
        param_layout.setSpacing(10)

        self.detection_interval_label = QLabel("检测间隔(帧):")
        self.detection_interval_label.setStyleSheet("color: #ecf0f1;")

        self.detection_interval_input = QSpinBox()
        self.detection_interval_input.setRange(1, 1000)
        self.detection_interval_input.setValue(25)
        self.detection_interval_input.setStyleSheet("background-color: white;")

        self.contour_threshold_label = QLabel("轮廓阈值:")
        self.contour_threshold_label.setStyleSheet("color: #ecf0f1;")

        self.contour_threshold_input = QSpinBox()
        self.contour_threshold_input.setRange(1, 1000)
        self.contour_threshold_input.setValue(150)
        self.contour_threshold_input.setStyleSheet("background-color: white;")

        self.confirm_param_button = QPushButton("应用参数")
        self.confirm_param_button.clicked.connect(self.on_confirm_params)
        self.confirm_param_button.setStyleSheet("background-color: #9b59b6;")

        param_layout.addWidget(self.detection_interval_label)
        param_layout.addWidget(self.detection_interval_input)
        param_layout.addWidget(self.contour_threshold_label)
        param_layout.addWidget(self.contour_threshold_input)
        param_layout.addWidget(self.confirm_param_button)
        param_layout.addStretch(1)

        main_layout.addLayout(param_layout)

        # 添加报警提示标签（左下角）
        self.alarm_status_label = QLabel("系统状态: 正常监控中")
        self.alarm_status_label.setStyleSheet("""
            QLabel {
                color: #2ecc71;
                font-size: 18px;
                font-weight: bold;
                padding: 10px;
                background-color: rgba(52, 73, 94, 0.8);
                border-radius: 5px;
                border: 2px solid #2ecc71;
            }
        """)
        main_layout.addWidget(self.alarm_status_label)

        self.setCentralWidget(main_widget)

        # 状态栏
        self.statusBar().showMessage("就绪 | 烟雾检测系统 V1.0")

        # 初始化pygame
        pygame.mixer.init()

    def on_confirm_params(self):
        new_interval = self.detection_interval_input.value()
        new_threshold = self.contour_threshold_input.value()

        for processor in self.processors:
            if processor:
                processor.update_params(new_interval, new_threshold)

        self.statusBar().showMessage(f"参数已更新: 检测间隔={new_interval}帧, 轮廓阈值={new_threshold}", 5000)

    def initialize_monitoring(self):
        try:
            # 停止所有更新器
            for updater in self.updaters:
                if updater is not None:
                    updater.stop()
                    updater.wait(1000)  # 最多等待1秒

            # 停止所有处理器
            for processor in self.processors:
                if processor is not None:
                    processor.stop()

            # 清空列表
            self.processors.clear()
            self.updaters.clear()

            # 重新初始化UI
            self.initUI()
            pygame.mixer.quit()
            pygame.mixer.init()

            self.statusBar().showMessage("系统已重新初始化", 3000)

        except Exception as e:
            logging.error(f"初始化监控错误: {str(e)}")
            QMessageBox.critical(self, "错误", f"初始化失败: {str(e)}")

    def on_select_stop(self):
        choice = self.comboBox.currentText()
        if choice == "选择监控点...":
            self.statusBar().showMessage("请先选择一个监控点", 3000)
            return

        try:
            index = int(choice.split("号")[0]) - 1  # 提取监控点编号
            if 0 <= index < len(self.processors) and self.processors[index]:
                self.processors[index].mute_alarm()
                self.number_label[index].setText("状态: 报警已暂停")
                self.number_label[index].setStyleSheet("color: #f39c12;")
                self.statusBar().showMessage(f"已暂停{choice}的报警", 3000)
        except Exception as e:
            logging.error(f"选择停止错误: {str(e)}")
            self.statusBar().showMessage(f"操作失败: {str(e)}", 5000)

    def exit_program(self):
        # 停止自动清理定时器
        if hasattr(self, 'cleanup_timer'):
            self.cleanup_timer.stop()
            print("🛑 自动清理定时器已停止")

        # 停止连接监控定时器
        if hasattr(self, 'connection_monitor_timer'):
            self.connection_monitor_timer.stop()
            print("🛑 连接监控定时器已停止")

        # 停止所有更新器
        for updater in self.updaters:
            if updater is not None:
                updater.stop()
                updater.wait(1000)

        # 停止所有处理器
        for processor in self.processors:
            if processor is not None:
                processor.stop()

        # 关闭线程池
        self.thread_pool.shutdown()

        # 退出应用
        pygame.quit()
        QApplication.instance().quit()

    def start_detection(self):
        num_streams = int(len(self.read_video_paths) / 2)
        print(num_streams)
        if num_streams == 0:
            self.statusBar().showMessage("错误: 没有可用的视频源", 5000)
            return

        for i in range(num_streams):
            if i >= len(self.processors):
                try:
                    # 创建并启动视频处理线程
                    processor = AreaAlarmVideoThread(
                        self.read_video_paths[i * 2],
                        i,
                        self.read_video_paths[i * 2 + 1]
                    )
                    processor.change_pixmap_signal.connect(lambda frame, idx=i: self.update_image(idx, frame))
                    processor.log_signal.connect(self.log_message)
                    processor.alarm_status_signal.connect(self.update_alarm_status)
                    processor.start()
                    self.processors.append(processor)

                    # 检查连接状态并更新UI
                    if processor.connection_failed:
                        self.number_label[i].setText("状态: 视频未连接")
                        self.number_label[i].setStyleSheet("color: #e74c3c;")
                        print(f"⚠️ 通道{i}连接失败，跳过该通道")
                    else:
                        self.number_label[i].setText("状态: 运行中")
                        self.number_label[i].setStyleSheet("color: #2ecc71;")
                        print(f"✅ 通道{i}线程启动成功")

                except Exception as e:
                    error_msg = f"❌ 通道{i}初始化失败: {str(e)}"
                    print(error_msg)
                    self.log_message(error_msg)
                    self.number_label[i].setText("状态: 初始化失败")
                    self.number_label[i].setStyleSheet("color: #e74c3c;")
                    # 添加一个空的处理器占位符
                    self.processors.append(None)
            else:
                # 恢复检测
                if self.processors[i]:
                    if self.processors[i].connection_failed:
                        self.number_label[i].setText("状态: 视频未连接")
                        self.number_label[i].setStyleSheet("color: #e74c3c;")
                    else:
                        self.processors[i].resume_detection()
                        self.number_label[i].setText("状态: 运行中")
                        self.number_label[i].setStyleSheet("color: #2ecc71;")

        self.statusBar().showMessage(f"已启动{num_streams}个监控点的检测", 3000)

    def update_image(self, label_index, frame):
        if label_index < len(self.image_label) and frame is not None:
            try:
                # 转换颜色空间
                rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                # 获取图像尺寸
                h, w, ch = rgb_image.shape
                bytes_per_line = ch * w

                # 创建QImage
                q_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)

                # 转换为QPixmap
                pixmap = QPixmap.fromImage(q_image)

                # 缩放以适应标签
                scaled_pixmap = pixmap.scaled(
                    self.image_label[label_index].width(),
                    self.image_label[label_index].height(),
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )

                # 设置到标签
                self.image_label[label_index].setPixmap(scaled_pixmap)

            except Exception as e:
                logging.error(f"更新图像错误: {str(e)}")
                self.number_label[label_index].setText("状态: 图像错误")
                self.number_label[label_index].setStyleSheet("color: #e74c3c;")

    def stop_detection(self):
        for i, processor in enumerate(self.processors):
            if processor is not None:
                processor.mute_alarm()
                self.number_label[i].setText("状态: 报警已停止")
                self.number_label[i].setStyleSheet("color: #f39c12;")

        self.statusBar().showMessage("已停止所有监控点的报警", 3000)

    def open_alarm_folder(self):
        if os.path.exists(self.output_dir):
            os.startfile(self.output_dir)
        else:
            self.statusBar().showMessage("报警目录不存在", 3000)

    def open_video_paths(self):
        if os.path.exists(self.txt_file_path):
            os.startfile(self.txt_file_path)
        else:
            self.statusBar().showMessage("视频源配置文件不存在", 3000)

    def closeEvent(self, event):
        try:
            # 停止自动清理定时器
            if hasattr(self, 'cleanup_timer'):
                self.cleanup_timer.stop()
                print("🛑 自动清理定时器已停止")

            # 停止连接监控定时器
            if hasattr(self, 'connection_monitor_timer'):
                self.connection_monitor_timer.stop()
                print("🛑 连接监控定时器已停止")

            # 停止所有更新器
            for updater in self.updaters:
                if updater is not None:
                    updater.stop()
                    updater.wait(1000)

            # 停止所有处理器
            for processor in self.processors:
                if processor is not None:
                    processor.stop()

            # 关闭线程池
            self.thread_pool.shutdown()

            # 清理pygame
            pygame.mixer.quit()
            pygame.quit()

            super().closeEvent(event)
        except Exception as e:
            logging.error(f"关闭窗口错误: {str(e)}")
            event.accept()

    def log_message(self, message):
        if self.log_window:
            self.log_window.logTextEdit.append(message)
        self.statusBar().showMessage(message, 5000)

    def update_alarm_status(self, channel_id, channel_name, is_alarm):
        """更新界面左下角的报警状态提示"""
        if is_alarm:
            # 报警状态
            self.alarm_status_label.setText(f"🚨 警报: 通道{channel_id+1}({channel_name}) 检测到烟雾警报！")
            self.alarm_status_label.setStyleSheet("""
                QLabel {
                    color: #e74c3c;
                    font-size: 18px;
                    font-weight: bold;
                    padding: 10px;
                    background-color: rgba(231, 76, 60, 0.2);
                    border-radius: 5px;
                    border: 2px solid #e74c3c;
                }
            """)
        else:
            # 恢复正常状态
            self.alarm_status_label.setText("系统状态: 正常监控中")
            self.alarm_status_label.setStyleSheet("""
                QLabel {
                    color: #2ecc71;
                    font-size: 18px;
                    font-weight: bold;
                    padding: 10px;
                    background-color: rgba(52, 73, 94, 0.8);
                    border-radius: 5px;
                    border: 2px solid #2ecc71;
                }
            """)

    def show_log_window(self):
        if self.log_window is None:
            self.log_window = LogWindow(self)
            self.log_window.logTextEdit.setStyleSheet("font-size: 14pt; font-family: Consolas;")
        self.log_window.show()

    def setup_auto_cleanup(self):
        """设置自动清理功能，每15天清空一次detection_results文件夹"""
        try:
            # 创建定时器，每24小时检查一次
            self.cleanup_timer = QTimer()
            self.cleanup_timer.timeout.connect(self.check_and_cleanup_results)
            self.cleanup_timer.start(24 * 60 * 60 * 1000)  # 24小时 = 86400000毫秒

            # 启动时也检查一次
            self.check_and_cleanup_results()

            print("✅ 自动清理功能已启用，每15天自动清空detection_results文件夹")
            write_log("自动清理功能已启用，每15天自动清空detection_results文件夹")

        except Exception as e:
            print(f"⚠️ 自动清理功能初始化失败: {e}")
            write_log(f"自动清理功能初始化失败: {e}")

    def check_and_cleanup_results(self):
        """检查并清理detection_results文件夹"""
        try:
            results_dir = 'detection_results'
            if not os.path.exists(results_dir):
                return

            # 检查文件夹的创建时间或最后修改时间
            cleanup_flag_file = os.path.join(results_dir, '.last_cleanup')
            current_time = datetime.now()

            should_cleanup = False

            if os.path.exists(cleanup_flag_file):
                # 读取上次清理时间
                try:
                    with open(cleanup_flag_file, 'r', encoding='utf-8') as f:
                        last_cleanup_str = f.read().strip()
                        last_cleanup = datetime.fromisoformat(last_cleanup_str)

                    # 检查是否超过15天
                    if (current_time - last_cleanup).days >= 15:
                        should_cleanup = True
                        print(f"📅 距离上次清理已过{(current_time - last_cleanup).days}天，需要清理")

                except Exception as e:
                    print(f"⚠️ 读取清理标记文件失败: {e}")
                    should_cleanup = True  # 如果读取失败，执行清理
            else:
                # 如果没有清理标记文件，检查文件夹中最旧的文件
                try:
                    # 获取所有文件
                    all_files = []
                    for root, dirs, files in os.walk(results_dir):
                        for file in files:
                            if file != '.last_cleanup':  # 排除清理标记文件
                                file_path = os.path.join(root, file)
                                all_files.append(file_path)

                    if all_files:
                        # 找到最旧的文件
                        oldest_file = min(all_files, key=os.path.getctime)
                        oldest_time = datetime.fromtimestamp(os.path.getctime(oldest_file))

                        if (current_time - oldest_time).days >= 15:
                            should_cleanup = True
                            print(f"📅 最旧文件创建于{(current_time - oldest_time).days}天前，需要清理")

                except Exception as e:
                    print(f"⚠️ 检查文件创建时间失败: {e}")

            if should_cleanup:
                self.cleanup_detection_results()

        except Exception as e:
            print(f"⚠️ 检查清理条件时出错: {e}")
            write_log(f"检查清理条件时出错: {e}")

    def cleanup_detection_results(self):
        """清理detection_results文件夹"""
        try:
            results_dir = 'detection_results'
            if not os.path.exists(results_dir):
                return

            # 统计清理前的文件数量和大小
            file_count = 0
            total_size = 0

            for root, dirs, files in os.walk(results_dir):
                for file in files:
                    if file != '.last_cleanup':  # 保留清理标记文件
                        file_path = os.path.join(root, file)
                        try:
                            file_size = os.path.getsize(file_path)
                            total_size += file_size
                            file_count += 1
                        except:
                            pass

            if file_count == 0:
                print("📁 detection_results文件夹为空，无需清理")
                return

            # 执行清理
            cleaned_files = 0
            cleaned_size = 0

            for root, dirs, files in os.walk(results_dir):
                for file in files:
                    if file != '.last_cleanup':  # 保留清理标记文件
                        file_path = os.path.join(root, file)
                        try:
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            cleaned_files += 1
                            cleaned_size += file_size
                        except Exception as e:
                            print(f"⚠️ 删除文件失败 {file_path}: {e}")

            # 清理空的子文件夹
            for root, dirs, files in os.walk(results_dir, topdown=False):
                for dir_name in dirs:
                    dir_path = os.path.join(root, dir_name)
                    try:
                        if not os.listdir(dir_path):  # 如果文件夹为空
                            os.rmdir(dir_path)
                            print(f"🗑️ 删除空文件夹: {dir_path}")
                    except:
                        pass

            # 更新清理标记文件
            cleanup_flag_file = os.path.join(results_dir, '.last_cleanup')
            with open(cleanup_flag_file, 'w', encoding='utf-8') as f:
                f.write(datetime.now().isoformat())

            # 记录清理结果
            size_mb = cleaned_size / (1024 * 1024)
            cleanup_message = (f"🧹 自动清理完成: 删除了{cleaned_files}个文件，"
                             f"释放了{size_mb:.2f}MB空间")
            print(cleanup_message)
            write_log(cleanup_message)

            # 更新状态栏
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage(f"自动清理完成: 删除{cleaned_files}个文件", 5000)

        except Exception as e:
            error_msg = f"自动清理失败: {e}"
            print(f"❌ {error_msg}")
            write_log(error_msg)

    def setup_connection_monitor(self):
        """设置连接状态监控"""
        try:
            # 创建连接监控定时器，每30秒检查一次连接状态
            self.connection_monitor_timer = QTimer()
            self.connection_monitor_timer.timeout.connect(self.monitor_connection_status)
            self.connection_monitor_timer.start(30 * 1000)  # 30秒

            print("✅ 连接状态监控已启用，每30秒检查一次")
            write_log("连接状态监控已启用")

        except Exception as e:
            print(f"⚠️ 连接状态监控初始化失败: {e}")
            write_log(f"连接状态监控初始化失败: {e}")

    def monitor_connection_status(self):
        """监控连接状态"""
        try:
            if not hasattr(self, 'processors') or not self.processors:
                return

            total_channels = len(self.processors)
            connected_channels = 0
            failed_channels = []

            for i, processor in enumerate(self.processors):
                if processor is not None:
                    if hasattr(processor, 'connection_failed') and not processor.connection_failed:
                        connected_channels += 1
                    else:
                        failed_channels.append(i + 1)
                else:
                    failed_channels.append(i + 1)

            connection_rate = (connected_channels / total_channels * 100) if total_channels > 0 else 0

            # 更新状态栏信息
            status_msg = f"连接状态: {connected_channels}/{total_channels} ({connection_rate:.1f}%)"
            if failed_channels:
                status_msg += f" | 失败通道: {','.join(map(str, failed_channels))}"

            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage(status_msg, 30000)  # 显示30秒

            # 记录连接状态日志（仅在状态变化时）
            if not hasattr(self, '_last_connection_rate') or self._last_connection_rate != connection_rate:
                log_msg = f"连接状态监控: {connected_channels}/{total_channels}通道在线 ({connection_rate:.1f}%)"
                if failed_channels:
                    log_msg += f", 失败通道: {failed_channels}"
                write_log(log_msg)
                self._last_connection_rate = connection_rate

            # 如果连接率过低，发出警告
            if connection_rate < 50:
                warning_msg = f"⚠️ 连接率过低: {connection_rate:.1f}%, 请检查网络和设备状态"
                print(warning_msg)
                write_log(warning_msg)

        except Exception as e:
            print(f"⚠️ 连接状态监控出错: {e}")
            write_log(f"连接状态监控出错: {e}")
class LogWindow(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('系统日志')
        self.setGeometry(200, 200, 1000, 600)

        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)

        # 日志文本框
        self.logTextEdit = QTextEdit()
        self.logTextEdit.setReadOnly(True)
        self.logTextEdit.setStyleSheet("""
            QTextEdit {
                background-color: #1e272e;
                color: #ecf0f1;
                font-family: Consolas;
                font-size: 12pt;
                border: 1px solid #3498db;
            }
        """)

        # 控制按钮
        btn_layout = QHBoxLayout()
        self.clear_btn = QPushButton("清空日志")
        self.clear_btn.clicked.connect(self.clear_log)
        self.save_btn = QPushButton("保存日志")
        self.save_btn.clicked.connect(self.save_log)
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)

        btn_layout.addWidget(self.clear_btn)
        btn_layout.addWidget(self.save_btn)
        btn_layout.addStretch(1)
        btn_layout.addWidget(self.close_btn)

        layout.addWidget(self.logTextEdit, 1)
        layout.addLayout(btn_layout)

        self.setCentralWidget(central_widget)

        # 加载现有日志
        self.load_log()

    def load_log(self):
        log_file = "漏袋检测日志.txt"
        if os.path.exists(log_file):
            try:
                with open(log_file, "r", encoding="utf-8") as f:
                    self.logTextEdit.setText(f.read())
            except:
                pass

    def clear_log(self):
        self.logTextEdit.clear()

    def save_log(self):
        log_file = "漏袋检测日志.txt"
        try:
            with open(log_file, "w", encoding="utf-8") as f:
                f.write(self.logTextEdit.toPlainText())
            QMessageBox.information(self, "成功", "日志已保存")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")

if __name__ == '__main__':
    app = QApplication(sys.argv)

    try:
        main_window = MainWindow()
        main_window.show()

        write_log("烟雾报警检测系统启动")
        print("🚀 烟雾报警检测系统启动成功")

        sys.exit(app.exec_())

    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        sys.exit(1)
